import { OpenRouterService } from '@/lib/openrouter'

interface YouTubeScriptRequest {
  topic: string
  duration: string
  style: string
  targetAudience: string
  additionalNotes?: string
}

interface YouTubeScript {
  title: string
  hook: string
  introduction: string
  mainContent: string
  conclusion: string
  fullScript: string
  metadata: {
    estimatedLength: string
    wordCount: number
    keyTopics: string[]
    engagementScore: number
  }
}

interface ResearchData {
  competitorAnalysis: string[]
  trendingTopics: string[]
  keyInsights: string[]
}

export class YouTubeScriptAgent {
  private openRouter: OpenRouterService
  private agentName = 'YouTube Script Generator'

  constructor() {
    this.openRouter = new OpenRouterService({
      model: 'moonshotai/kimi-k2',
      temperature: 0.7,
      maxTokens: 16000 // Double the tokens as requested
    })
  }

  /**
   * Generate a complete YouTube script using OpenRouter + Kimi-K2
   */
  async generateScript(request: YouTubeScriptRequest): Promise<YouTubeScript> {
    console.log(`🎬 ${this.agentName}: Starting script generation for "${request.topic}"`)

    try {
      // Step 1: Research the topic
      const researchData = await this.conductResearch(request.topic)
      
      // Step 2: Generate the complete script
      const script = await this.createScript(request, researchData)
      
      // Step 3: Generate optimized title
      const title = await this.generateTitle(request.topic, request.style)
      
      console.log(`✅ ${this.agentName}: Script generation completed`)
      
      return {
        ...script,
        title,
        metadata: {
          estimatedLength: this.calculateDuration(script.fullScript, request.duration),
          wordCount: this.countWords(script.fullScript),
          keyTopics: this.extractKeyTopics(script.fullScript),
          engagementScore: this.calculateEngagementScore(script.fullScript)
        }
      }
    } catch (error) {
      console.error(`❌ ${this.agentName}: Script generation failed:`, error)
      throw new Error(`Script generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Conduct research using web search
   */
  private async conductResearch(topic: string): Promise<ResearchData> {
    console.log(`🔍 Researching topic: ${topic}`)

    const researchPrompt = `Research the topic "${topic}" for YouTube content creation. Provide:

1. COMPETITOR ANALYSIS: What are the top 5 most successful YouTube videos about this topic?
2. TRENDING ASPECTS: What are the current trending angles or perspectives on this topic?
3. KEY INSIGHTS: What are the most important points viewers want to know about this topic?

Focus on actionable insights that will make the content stand out and provide real value to viewers.

Format your response as structured data with clear sections.`

    try {
      const result = await this.openRouter.generateYouTubeContent(
        researchPrompt,
        '',
        { temperature: 0.3, maxTokens: 4000 },
        'Topic Research'
      )

      return this.parseResearchData(result.response)
    } catch (error) {
      console.warn('Research failed, using fallback data')
      return {
        competitorAnalysis: [`Popular videos about ${topic}`, `Educational content on ${topic}`, `Tutorial-style ${topic} videos`],
        trendingTopics: [`Latest trends in ${topic}`, `2025 updates on ${topic}`, `Advanced ${topic} techniques`],
        keyInsights: [`Core principles of ${topic}`, `Common mistakes in ${topic}`, `Best practices for ${topic}`]
      }
    }
  }

  /**
   * Create the complete script structure
   */
  private async createScript(request: YouTubeScriptRequest, research: ResearchData): Promise<Omit<YouTubeScript, 'title' | 'metadata'>> {
    const scriptPrompt = this.buildScriptPrompt(request, research)

    const result = await this.openRouter.generateYouTubeContent(
      scriptPrompt,
      '',
      { 
        temperature: 0.7, 
        maxTokens: 16000 // Double the tokens
      },
      'Script Generation'
    )

    return this.parseScriptResponse(result.response, request)
  }

  /**
   * Build comprehensive script generation prompt
   */
  private buildScriptPrompt(request: YouTubeScriptRequest, research: ResearchData): string {
    const durationGuidance = this.getDurationGuidance(request.duration)
    
    return `Create a compelling YouTube script about "${request.topic}" with the following specifications:

SCRIPT REQUIREMENTS:
- Duration: ${request.duration}
- Style: ${request.style}
- Target Audience: ${request.targetAudience}
- Additional Notes: ${request.additionalNotes || 'None'}

RESEARCH INSIGHTS:
Competitor Analysis: ${research.competitorAnalysis.join(', ')}
Trending Topics: ${research.trendingTopics.join(', ')}
Key Insights: ${research.keyInsights.join(', ')}

SCRIPT STRUCTURE:
1. HOOK (First 15 seconds): Create an attention-grabbing opening that immediately hooks viewers
2. INTRODUCTION (Next 30-45 seconds): Establish credibility and preview what viewers will learn
3. MAIN CONTENT (${durationGuidance.mainContentTime}): Deliver the core value with clear sections
4. CONCLUSION (Final 60-90 seconds): Summarize key points and strong call-to-action

CONTENT GUIDELINES:
- Write in a conversational, engaging tone
- Include specific examples and actionable advice
- Add natural transition phrases between sections
- Include engagement hooks throughout (questions, challenges, etc.)
- Reference current trends and 2025 context
- Make it feel authentic and valuable

TARGET WORD COUNT: ${durationGuidance.wordCount} words

Please provide the complete script with clear section markers:
[HOOK]
[INTRODUCTION] 
[MAIN CONTENT]
[CONCLUSION]

Make this script compelling, informative, and optimized for YouTube success.`
  }

  /**
   * Generate optimized title
   */
  private async generateTitle(topic: string, style: string): Promise<string> {
    const titlePrompt = `Create 5 compelling YouTube titles for a ${style} video about "${topic}". 

Requirements:
- Optimized for YouTube algorithm and click-through rate
- Include power words and emotional triggers
- Reference 2025 for freshness
- 60 characters or less for mobile optimization
- Match the ${style} style

Provide 5 options and mark the best one with [BEST].`

    try {
      const result = await this.openRouter.generateYouTubeContent(
        titlePrompt,
        '',
        { temperature: 0.8, maxTokens: 500 },
        'Title Generation'
      )

      return this.extractBestTitle(result.response) || `${topic}: Complete 2025 Guide`
    } catch (error) {
      return `${topic}: Complete 2025 Guide`
    }
  }

  /**
   * Parse research data from AI response
   */
  private parseResearchData(response: string): ResearchData {
    const competitorMatch = response.match(/COMPETITOR ANALYSIS:?\s*(.*?)(?=TRENDING|$)/is)
    const trendingMatch = response.match(/TRENDING.*?:?\s*(.*?)(?=KEY INSIGHTS|$)/is)
    const insightsMatch = response.match(/KEY INSIGHTS:?\s*(.*?)$/is)

    return {
      competitorAnalysis: this.extractListItems(competitorMatch?.[1] || ''),
      trendingTopics: this.extractListItems(trendingMatch?.[1] || ''),
      keyInsights: this.extractListItems(insightsMatch?.[1] || '')
    }
  }

  /**
   * Parse script response into structured format
   */
  private parseScriptResponse(response: string, request: YouTubeScriptRequest): Omit<YouTubeScript, 'title' | 'metadata'> {
    const hookMatch = response.match(/\[HOOK\](.*?)(?=\[INTRODUCTION\]|$)/is)
    const introMatch = response.match(/\[INTRODUCTION\](.*?)(?=\[MAIN CONTENT\]|$)/is)
    const mainMatch = response.match(/\[MAIN CONTENT\](.*?)(?=\[CONCLUSION\]|$)/is)
    const conclusionMatch = response.match(/\[CONCLUSION\](.*?)$/is)

    const hook = this.cleanSection(hookMatch?.[1] || this.generateFallbackHook(request.topic))
    const introduction = this.cleanSection(introMatch?.[1] || this.generateFallbackIntro(request.topic))
    const mainContent = this.cleanSection(mainMatch?.[1] || this.generateFallbackMain(request.topic))
    const conclusion = this.cleanSection(conclusionMatch?.[1] || this.generateFallbackConclusion(request.topic))

    const fullScript = this.buildFullScript(hook, introduction, mainContent, conclusion)

    return {
      hook,
      introduction,
      mainContent,
      conclusion,
      fullScript
    }
  }

  /**
   * Helper methods for script processing
   */
  private getDurationGuidance(duration: string) {
    const guides = {
      '1-3': { wordCount: 2400, mainContentTime: '1-2 minutes' },
      '3-7': { wordCount: 5600, mainContentTime: '3-5 minutes' },
      '7-15': { wordCount: 12000, mainContentTime: '6-12 minutes' },
      '15+': { wordCount: 24000, mainContentTime: '12+ minutes' }
    }
    return guides[duration as keyof typeof guides] || guides['3-7']
  }

  private extractListItems(text: string): string[] {
    const items = text.match(/(?:^|\n)\s*[-•*]\s*(.+)/gm) || 
                  text.match(/(?:^|\n)\s*\d+\.\s*(.+)/gm) ||
                  text.split('\n').filter(line => line.trim().length > 10)
    
    return items.slice(0, 5).map(item => item.replace(/^[-•*\d.\s]+/, '').trim())
  }

  private extractBestTitle(response: string): string | null {
    const bestMatch = response.match(/\[BEST\]\s*(.+)/i)
    if (bestMatch) return bestMatch[1].trim()

    const titles = response.match(/^.+$/gm)?.filter(line => 
      line.length > 10 && line.length < 80 && !line.includes('[')
    )
    return titles?.[0]?.trim() || null
  }

  private cleanSection(content: string): string {
    return content.trim()
      .replace(/^\[.*?\]\s*/, '')
      .replace(/\n\s*\n/g, '\n\n')
      .trim()
  }

  private buildFullScript(hook: string, intro: string, main: string, conclusion: string): string {
    return `## 🎯 Hook (0:00-0:15)
${hook}

## 👋 Introduction (0:15-1:00)
${intro}

## 📚 Main Content
${main}

## 🎬 Conclusion
${conclusion}

---
*Generated with AI-powered YouTube Script Generator using OpenRouter + Kimi-K2*`
  }

  private generateFallbackHook(topic: string): string {
    return `What if I told you that everything you think you know about ${topic} is about to change? In the next few minutes, I'm going to show you exactly what you need to know to master ${topic} in 2025.`
  }

  private generateFallbackIntro(topic: string): string {
    return `Welcome back to the channel! Today we're diving deep into ${topic}, and I promise by the end of this video, you'll have a complete understanding that most people take years to develop. Let's get started.`
  }

  private generateFallbackMain(topic: string): string {
    return `Let me break down ${topic} into the key components you need to understand. First, we'll cover the fundamentals, then move into practical applications, and finally discuss advanced strategies that will set you apart from everyone else.`
  }

  private generateFallbackConclusion(topic: string): string {
    return `So there you have it - everything you need to know about ${topic} to get started and see real results. What questions do you have? Drop them in the comments below, and don't forget to subscribe for more content like this!`
  }

  private countWords(text: string): number {
    return text.split(/\s+/).filter(word => word.length > 0).length
  }

  private calculateDuration(script: string, targetDuration: string): string {
    const wordCount = this.countWords(script)
    const estimatedMinutes = Math.ceil(wordCount / 150) // 150 words per minute for video
    return `${estimatedMinutes} minutes (${wordCount} words)`
  }

  private extractKeyTopics(script: string): string[] {
    // Simple keyword extraction - could be enhanced with NLP
    const words = script.toLowerCase().match(/\b\w{4,}\b/g) || []
    const frequency: { [key: string]: number } = {}
    
    words.forEach(word => {
      if (!['this', 'that', 'with', 'have', 'will', 'from', 'they', 'been', 'said', 'each', 'which', 'their', 'time', 'about'].includes(word)) {
        frequency[word] = (frequency[word] || 0) + 1
      }
    })

    return Object.entries(frequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([word]) => word)
  }

  private calculateEngagementScore(script: string): number {
    let score = 70 // Base score
    
    // Check for engagement elements
    if (script.includes('?')) score += 5 // Questions
    if (script.includes('comment')) score += 5 // Call for comments
    if (script.includes('subscribe')) score += 5 // Subscribe CTA
    if (script.includes('like')) score += 3 // Like CTA
    if (script.match(/\b(you|your)\b/gi)?.length > 10) score += 5 // Personal pronouns
    if (script.includes('2025')) score += 3 // Current year reference
    
    return Math.min(score, 95) // Cap at 95
  }
}
