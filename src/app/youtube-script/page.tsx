'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import {
  ArrowLeft,
  Video,
  Search,
  Zap,
  Clock,
  Users,
  Target,
  FileText,
  Download,
  Copy,
  Play,
  BarChart3,
  Sparkles,
  CheckCircle,
  Loader2,
  Youtube
} from 'lucide-react'
import Link from 'next/link'
import ReactMarkdown from 'react-markdown'

// Function to preprocess script content for better formatting
const preprocessScript = (script: string): string => {
  if (!script || typeof script !== 'string') {
    return 'No script content available'
  }
  
  return script
    // Handle **Title:** format at the beginning
    .replace(/^\*\*Title:\*\*\s*(.*?)$/gim, '# 🎬 $1\n')
    
    // Handle **Hook:** format
    .replace(/^\*\*Hook:\*\*\s*(.*?)$/gim, '## 🎯 Hook\n\n$1\n')
    
    // Handle **Full Script:** format
    .replace(/^\*\*Full Script:\*\*\s*$/gim, '## 📝 Full Script\n')
    
    // Clean up timestamp patterns - convert complex timestamp sections
    .replace(/\*\*\[(\d+:\d+)\]\s*-\s*\[(\d+:\d+)\]\s*\)\s*(.*?)\*\*/g, '\n\n---\n### <div class="section-header"><span class="timestamp-range">[$1 - $2]</span> $3</div>\n')
    
    // Convert timestamp ranges with section names 
    .replace(/\*\*(\d+:\d+)-(\d+:\d+)\s*-?\s*(.*?)\*\*/g, '\n\n---\n### <div class="section-header"><span class="timestamp-range">[$1 - $2]</span> $3</div>\n')
    
    // Convert standalone section headers
    .replace(/\)\s*(HOOK|RETENTION FOUNDATION|INTRODUCTION|ENGAGEMENT INTEGRATION|PERFORMANCE SCRUTINY|COMMUNITY BUILDING)\s*\(/gi, '\n\n---\n### <div class="section-header">🎯 $1</div>\n')
    
    // Handle speaker tags more cleanly
    .replace(/VOICEOVER:\s*/g, '\n\n<div class="speaker-tag">🎤 Host</div>\n')
    .replace(/\*\*Host:\*\*/g, '\n\n<div class="speaker-tag">🎤 Host</div>\n')
    .replace(/HOST:\s*/g, '\n\n<div class="speaker-tag">🎤 Host</div>\n')
    
    // Remove visual directions completely - they're production notes, not script content
    .replace(/\(Visual:\s*(.*?)\)/gi, '')
    .replace(/\((.*?B-roll.*?|.*?graphics.*?|.*?interface.*?|.*?screen.*?|.*?animated.*?|.*?shows.*?|.*?appears.*?|.*?transitions.*?)\)/gi, '')
    .replace(/\*\*\((.*?Visual.*?|.*?B-roll.*?|.*?graphics.*?)\)\*\*/gi, '')
    
    // Handle engagement elements but keep them inline (no special blocks)
    .replace(/(What.*?\?.*?Let me know.*?comments.*?below!?)/gi, '$1')
    .replace(/(Share your.*?experiences?!?)/gi, '$1')
    
    // Keep subscribe/like mentions inline without special formatting
    .replace(/(hit that like button|subscribe|notification bell)/gi, '$1')
    
    // Clean up quotation marks around speech
    .replace(/"\s*(.*?)\s*"/g, '$1')
    
    // Convert remaining bold sections to emphasis
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    
    // Clean up italic emphasis  
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    
    // Clean up multiple separators and spacing
    .replace(/---+/g, '---')
    .replace(/\n{4,}/g, '\n\n\n')
    .replace(/^\s*\n/gm, '\n')
    .trim()
}

// Video duration options
const DURATION_OPTIONS = [
  { id: '1-3', label: '1-3 minutes', icon: '⚡', description: 'Quick, punchy content' },
  { id: '3-7', label: '3-7 minutes', icon: '🎯', description: 'Standard engagement' },
  { id: '7-15', label: '7-15 minutes', icon: '📚', description: 'In-depth content' },
  { id: '15+', label: '15+ minutes', icon: '🎬', description: 'Long-form content' }
]

// Video styles
const STYLE_OPTIONS = [
  { id: 'educational', label: 'Educational', icon: '🎓', description: 'Teach and inform' },
  { id: 'entertainment', label: 'Entertainment', icon: '🎪', description: 'Fun and engaging' },
  { id: 'tutorial', label: 'Tutorial', icon: '🛠️', description: 'Step-by-step guide' },
  { id: 'review', label: 'Review', icon: '⭐', description: 'Product/service review' },
  { id: 'vlog', label: 'Vlog', icon: '📱', description: 'Personal storytelling' },
  { id: 'explainer', label: 'Explainer', icon: '💡', description: 'Concept breakdown' }
]

// Target audiences
const AUDIENCE_OPTIONS = [
  { id: 'general', label: 'General Audience', description: 'Broad appeal content' },
  { id: 'tech', label: 'Tech Enthusiasts', description: 'Technology-focused viewers' },
  { id: 'business', label: 'Business Professionals', description: 'Career and business content' },
  { id: 'students', label: 'Students & Learners', description: 'Educational content seekers' },
  { id: 'creators', label: 'Content Creators', description: 'Fellow creators and influencers' },
  { id: 'lifestyle', label: 'Lifestyle & Wellness', description: 'Health, fitness, and lifestyle' }
]

// Analysis phases
const ANALYSIS_PHASES = [
  { id: 'search', label: 'YouTube Search', description: 'Finding top videos for your topic' },
  { id: 'captions', label: 'Caption Extraction', description: 'Extracting transcripts from videos' },
  { id: 'analysis', label: 'Pattern Analysis', description: 'Analyzing hooks and engagement strategies' },
  { id: 'generation', label: 'Gemini Generation', description: 'Creating your optimized script with Gemini' }
]

interface AnalysisProgress {
  currentPhase: number
  phases: {
    search: { status: 'pending' | 'running' | 'complete', data?: any }
    captions: { status: 'pending' | 'running' | 'complete', data?: any }
    analysis: { status: 'pending' | 'running' | 'complete', data?: any }
    generation: { status: 'pending' | 'running' | 'complete', data?: any }
  }
}

interface GeneratedScript {
  title: string
  hook: string
  introduction: string
  mainContent: string
  conclusion: string
  fullScript: string
  timestamps: Array<{ time: string, section: string, content: string }>
  engagementElements: Array<{ type: string, timing: string, description: string }>
  visualCues: Array<{ timing: string, suggestion: string }>
  metadata: {
    estimatedLength: string
    keyTopics: string[]
    engagementScore: number
    researchVideos: Array<{ title: string, views: number, engagement: number }>
  }
}

export default function YouTubeScriptGeneratorPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  // Form state
  const [topic, setTopic] = useState('')
  const [duration, setDuration] = useState('3-7')
  const [style, setStyle] = useState('educational')
  const [targetAudience, setTargetAudience] = useState('general')
  const [additionalNotes, setAdditionalNotes] = useState('')

  // Generation state
  const [isGenerating, setIsGenerating] = useState(false)
  const [analysisProgress, setAnalysisProgress] = useState<AnalysisProgress>({
    currentPhase: -1,
    phases: {
      search: { status: 'pending' },
      captions: { status: 'pending' },
      analysis: { status: 'pending' },
      generation: { status: 'pending' }
    }
  })
  const [generatedScript, setGeneratedScript] = useState<GeneratedScript | null>(null)
  const [activeTab, setActiveTab] = useState('input')
  const [copied, setCopied] = useState(false)

  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login')
    }
  }, [status, router])

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin w-12 h-12 border-2 border-red-400 border-t-transparent rounded-full mx-auto"></div>
          <p className="text-gray-400">Loading...</p>
        </div>
      </div>
    )
  }

  if (status === 'unauthenticated') {
    return null
  }

  const handleGenerate = async () => {
    if (!topic.trim()) return

    setIsGenerating(true)
    setActiveTab('progress')
    
    // Generate session ID for progress tracking
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    // Reset progress
    setAnalysisProgress({
      currentPhase: 0,
      phases: {
        search: { status: 'pending' },
        captions: { status: 'pending' },
        analysis: { status: 'pending' },
        generation: { status: 'pending' }
      }
    })

    // Set up SSE for real-time progress
    const eventSource = new EventSource(`/api/generate/youtube-script/progress?sessionId=${sessionId}`)
    
    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        
        if (data.type === 'progress') {
          console.log('Progress update:', data)
          
          // Update progress based on phase
          const phaseMap: Record<string, keyof AnalysisProgress['phases']> = {
            'search': 'search',
            'captions': 'captions',
            'analysis': 'analysis',
            'generation': 'generation'
          }
          
          const phaseKey = phaseMap[data.phase]
          if (phaseKey) {
            setAnalysisProgress(prev => ({
              currentPhase: data.currentPhase || prev.currentPhase,
              phases: {
                ...prev.phases,
                [phaseKey]: { 
                  status: data.phase === 'completed' ? 'complete' : 'running',
                  data: data
                }
              }
            }))
          }
        } else if (data.type === 'completed') {
          console.log('Generation completed')
          eventSource.close()
        }
      } catch (error) {
        console.error('SSE parsing error:', error)
      }
    }
    
    eventSource.onerror = (error) => {
      console.error('SSE error:', error)
      eventSource.close()
    }

    try {
      const response = await fetch('/api/generate/youtube-script', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          topic,
          duration,
          style,
          targetAudience,
          additionalNotes,
          sessionId
        })
      })

      const data = await response.json()
      
      if (data.success) {
        setGeneratedScript(data.script)
        setActiveTab('script')
        
        // Mark all phases as complete
        setAnalysisProgress(prev => ({
          currentPhase: 3,
          phases: {
            search: { status: 'complete' },
            captions: { status: 'complete' },
            analysis: { status: 'complete' },
            generation: { status: 'complete' }
          }
        }))
      } else {
        alert('Error: ' + data.error)
      }
    } catch (error) {
      console.error('Generation error:', error)
      alert('Failed to generate YouTube script')
    } finally {
      setIsGenerating(false)
      eventSource.close()
    }
  }

  const copyToClipboard = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy:', error)
    }
  }

  const downloadScript = () => {
    if (!generatedScript) return
    
    const element = document.createElement('a')
    const file = new Blob([generatedScript.fullScript], { type: 'text/plain' })
    element.href = URL.createObjectURL(file)
    element.download = `youtube-script-${topic.replace(/[^a-z0-9]/gi, '_')}.txt`
    document.body.appendChild(element)
    element.click()
    document.body.removeChild(element)
  }

  return (
    <div className="min-h-screen bg-black">
      {/* Animated Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-red-900/20 via-black to-orange-900/20" />
        <motion.div
          animate={{
            x: [0, 100, 0],
            y: [0, -100, 0],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-red-500/10 rounded-full blur-[100px]"
        />
        <motion.div
          animate={{
            x: [0, -100, 0],
            y: [0, 100, 0],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute bottom-1/4 right-1/4 w-[600px] h-[600px] bg-orange-500/10 rounded-full blur-[120px]"
        />
      </div>

      {/* Header */}
      <div className="relative z-10">
        <div className="border-b border-white/10 bg-black/60 backdrop-blur-xl">
          <div className="max-w-7xl mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Link href="/dashboard">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    className="p-2 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 transition-all"
                  >
                    <ArrowLeft className="w-5 h-5 text-white" />
                  </motion.button>
                </Link>
                <div className="flex items-center space-x-3">
                  <div className="p-3 rounded-2xl bg-gradient-to-br from-red-600 to-orange-600">
                    <Video className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-white">YouTube Script Generator</h1>
                    <p className="text-gray-400">Gemini-Powered Script Creation with Research & Analysis</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex space-x-1 bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl p-1">
            <button
              onClick={() => setActiveTab('input')}
              className={`flex-1 px-4 py-2 rounded-lg transition-all ${
                activeTab === 'input'
                  ? 'bg-red-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-white/10'
              }`}
            >
              <div className="flex items-center justify-center space-x-2">
                <Target className="w-4 h-4" />
                <span>Script Details</span>
              </div>
            </button>
            <button
              onClick={() => setActiveTab('progress')}
              className={`flex-1 px-4 py-2 rounded-lg transition-all ${
                activeTab === 'progress'
                  ? 'bg-red-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-white/10'
              }`}
              disabled={!isGenerating && analysisProgress.currentPhase === -1}
            >
              <div className="flex items-center justify-center space-x-2">
                <BarChart3 className="w-4 h-4" />
                <span>Analysis Progress</span>
              </div>
            </button>
            <button
              onClick={() => setActiveTab('script')}
              className={`flex-1 px-4 py-2 rounded-lg transition-all ${
                activeTab === 'script'
                  ? 'bg-red-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-white/10'
              }`}
              disabled={!generatedScript}
            >
              <div className="flex items-center justify-center space-x-2">
                <FileText className="w-4 h-4" />
                <span>Generated Script</span>
              </div>
            </button>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-6 pb-8">
          <AnimatePresence mode="wait">
            {activeTab === 'input' && (
              <motion.div
                key="input"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="space-y-8"
              >
                {/* Topic Input */}
                <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                  <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
                    <Search className="w-5 h-5 mr-2 text-red-400" />
                    Video Topic
                  </h2>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Main Topic/Keyword *
                      </label>
                      <input
                        type="text"
                        value={topic}
                        onChange={(e) => setTopic(e.target.value)}
                        placeholder="e.g., How to learn AI programming, Best productivity tips..."
                        className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-red-500/50 transition-all"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Additional Notes (optional)
                      </label>
                      <textarea
                        value={additionalNotes}
                        onChange={(e) => setAdditionalNotes(e.target.value)}
                        placeholder="Any specific points, style preferences, or requirements..."
                        className="w-full h-24 px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-red-500/50 transition-all resize-none"
                      />
                    </div>
                  </div>
                </div>

                {/* Configuration Options */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Duration */}
                  <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                    <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                      <Clock className="w-5 h-5 mr-2 text-red-400" />
                      Video Duration
                    </h3>
                    <div className="space-y-3">
                      {DURATION_OPTIONS.map((option) => (
                        <button
                          key={option.id}
                          onClick={() => setDuration(option.id)}
                          className={`w-full p-3 rounded-lg border text-left transition-all ${
                            duration === option.id
                              ? 'bg-red-600/20 border-red-500/50 text-white'
                              : 'bg-white/5 border-white/20 text-gray-400 hover:bg-white/10'
                          }`}
                        >
                          <div className="flex items-center space-x-3">
                            <span className="text-lg">{option.icon}</span>
                            <div>
                              <div className="font-medium">{option.label}</div>
                              <div className="text-xs opacity-75">{option.description}</div>
                            </div>
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Style */}
                  <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                    <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                      <Sparkles className="w-5 h-5 mr-2 text-red-400" />
                      Video Style
                    </h3>
                    <div className="space-y-3">
                      {STYLE_OPTIONS.map((option) => (
                        <button
                          key={option.id}
                          onClick={() => setStyle(option.id)}
                          className={`w-full p-3 rounded-lg border text-left transition-all ${
                            style === option.id
                              ? 'bg-red-600/20 border-red-500/50 text-white'
                              : 'bg-white/5 border-white/20 text-gray-400 hover:bg-white/10'
                          }`}
                        >
                          <div className="flex items-center space-x-3">
                            <span className="text-lg">{option.icon}</span>
                            <div>
                              <div className="font-medium">{option.label}</div>
                              <div className="text-xs opacity-75">{option.description}</div>
                            </div>
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Target Audience */}
                  <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                    <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                      <Users className="w-5 h-5 mr-2 text-red-400" />
                      Target Audience
                    </h3>
                    <div className="space-y-3">
                      {AUDIENCE_OPTIONS.map((option) => (
                        <button
                          key={option.id}
                          onClick={() => setTargetAudience(option.id)}
                          className={`w-full p-3 rounded-lg border text-left transition-all ${
                            targetAudience === option.id
                              ? 'bg-red-600/20 border-red-500/50 text-white'
                              : 'bg-white/5 border-white/20 text-gray-400 hover:bg-white/10'
                          }`}
                        >
                          <div className="font-medium">{option.label}</div>
                          <div className="text-xs opacity-75">{option.description}</div>
                        </button>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Generate Button */}
                <div className="text-center">
                  <motion.button
                    onClick={handleGenerate}
                    disabled={!topic.trim() || isGenerating}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="px-8 py-4 bg-gradient-to-r from-red-600 to-orange-600 text-white font-semibold rounded-xl hover:from-red-700 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center space-x-2 mx-auto"
                  >
                    {isGenerating ? (
                      <>
                        <Loader2 className="w-5 h-5 animate-spin" />
                        <span>Generating Script...</span>
                      </>
                    ) : (
                      <>
                        <Youtube className="w-5 h-5" />
                        <span>Generate YouTube Script</span>
                      </>
                    )}
                  </motion.button>
                </div>
              </motion.div>
            )}

            {activeTab === 'progress' && (
              <motion.div
                key="progress"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                className="space-y-6"
              >
                <div className="text-center mb-8">
                  <h2 className="text-2xl font-bold text-white mb-2">Analyzing YouTube Content</h2>
                  <p className="text-gray-400">Please wait while we research and analyze the best content for your script</p>
                </div>

                <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                  <div className="space-y-6">
                    {ANALYSIS_PHASES.map((phase, index) => {
                      const phaseKey = phase.id as keyof AnalysisProgress['phases']
                      const phaseStatus = analysisProgress.phases[phaseKey]?.status || 'pending'
                      const isActive = analysisProgress.currentPhase === index
                      const isComplete = phaseStatus === 'complete'
                      const isRunning = phaseStatus === 'running'

                      return (
                        <div key={phase.id} className="flex items-center space-x-4">
                          <div className={`w-12 h-12 rounded-full flex items-center justify-center border-2 ${
                            isComplete 
                              ? 'bg-green-600/20 border-green-500/50 text-green-400'
                              : isRunning
                              ? 'bg-red-600/20 border-red-500/50 text-red-400'
                              : 'bg-white/5 border-white/20 text-gray-400'
                          }`}>
                            {isComplete ? (
                              <CheckCircle className="w-6 h-6" />
                            ) : isRunning ? (
                              <Loader2 className="w-6 h-6 animate-spin" />
                            ) : (
                              <div className="w-4 h-4 rounded-full bg-current opacity-50" />
                            )}
                          </div>
                          
                          <div className="flex-1">
                            <h3 className={`font-semibold ${
                              isComplete ? 'text-green-400' : isRunning ? 'text-red-400' : 'text-gray-400'
                            }`}>
                              {phase.label}
                            </h3>
                            <p className="text-sm text-gray-500">{phase.description}</p>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>
              </motion.div>
            )}

            {activeTab === 'script' && generatedScript && (
              <motion.div
                key="script"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                className="space-y-6"
              >
                {/* Action Bar */}
                <div className="flex items-center justify-between bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl p-4">
                  <div className="text-white">
                    <h2 className="font-semibold">{generatedScript.title}</h2>
                    <p className="text-sm text-gray-400">
                      Estimated length: {generatedScript.metadata.estimatedLength} | 
                      Engagement score: {generatedScript.metadata.engagementScore}/100
                    </p>
                  </div>
                  <div className="flex items-center space-x-3">
                    <motion.button
                      onClick={() => copyToClipboard(generatedScript.fullScript)}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors"
                    >
                      {copied ? <CheckCircle className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                      <span>{copied ? 'Copied!' : 'Copy'}</span>
                    </motion.button>
                    <motion.button
                      onClick={downloadScript}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                    >
                      <Download className="w-4 h-4" />
                      <span>Download</span>
                    </motion.button>
                  </div>
                </div>

                {/* Script Content */}
                <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                  {/* Enhanced CSS for markdown script formatting */}
                  <style jsx global>{`
                    .timestamp {
                      display: inline-block;
                      background: linear-gradient(135deg, #dc2626, #ea580c);
                      color: white;
                      padding: 4px 8px;
                      border-radius: 6px;
                      font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
                      font-size: 0.75rem;
                      font-weight: 600;
                      margin-right: 8px;
                      box-shadow: 0 2px 4px rgba(0,0,0,0.3);
                      border: 1px solid rgba(255,255,255,0.1);
                    }
                    
                    .timestamp-range {
                      display: inline-block;
                      background: linear-gradient(135deg, #7c3aed, #a855f7);
                      color: white;
                      padding: 6px 12px;
                      border-radius: 8px;
                      font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
                      font-size: 0.875rem;
                      font-weight: 700;
                      margin-right: 12px;
                      box-shadow: 0 3px 6px rgba(124, 58, 237, 0.4);
                      border: 1px solid rgba(168, 85, 247, 0.3);
                    }
                    
                    .script-section {
                      margin: 2rem 0;
                      padding: 1.5rem;
                      background: rgba(255,255,255,0.03);
                      border-radius: 12px;
                      border-left: 4px solid #dc2626;
                    }
                    
                    
                    .stage-direction {
                      background: rgba(168, 85, 247, 0.1);
                      border-left: 4px solid #a855f7;
                      padding: 0.75rem 1rem;
                      margin: 0.5rem 0;
                      border-radius: 0 6px 6px 0;
                      font-style: italic;
                      color: #c4b5fd;
                      font-size: 0.9rem;
                    }
                    
                    .speaker-tag {
                      color: #fbbf24;
                      font-weight: 600;
                      display: inline-flex;
                      align-items: center;
                      gap: 0.5rem;
                      margin-bottom: 0.5rem;
                    }
                    
                    .script-content h2 {
                      background: linear-gradient(135deg, #dc2626, #ea580c);
                      background-clip: text;
                      -webkit-background-clip: text;
                      -webkit-text-fill-color: transparent;
                      padding: 1rem 0;
                      border-bottom: 2px solid rgba(220, 38, 38, 0.3);
                      margin-bottom: 1.5rem;
                    }
                    
                    .script-content p {
                      line-height: 1.8;
                      margin-bottom: 1.2rem;
                    }
                    
                    .script-content strong {
                      background: rgba(255, 255, 255, 0.1);
                      padding: 2px 6px;
                      border-radius: 4px;
                    }
                    
                    .pattern-interrupt {
                      background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 165, 0, 0.2));
                      border: 2px solid #ffd700;
                      border-radius: 12px;
                      padding: 1rem 1.5rem;
                      margin: 1.5rem 0;
                      color: #ffd700;
                      font-weight: 600;
                      box-shadow: 0 4px 6px rgba(255, 215, 0, 0.3);
                    }
                    
                    .engagement-cue {
                      background: rgba(34, 197, 94, 0.1);
                      border-left: 4px solid #22c55e;
                      padding: 1rem 1.5rem;
                      margin: 1rem 0;
                      border-radius: 0 8px 8px 0;
                      color: #86efac;
                      font-style: italic;
                    }
                    
                    .script-content h1 {
                      background: linear-gradient(135deg, #fbbf24, #f59e0b);
                      background-clip: text;
                      -webkit-background-clip: text;
                      -webkit-text-fill-color: transparent;
                      text-align: center;
                      padding: 1.5rem 0;
                      border-bottom: 3px solid rgba(251, 191, 36, 0.3);
                      margin-bottom: 2rem;
                      font-size: 2.5rem;
                    }
                    
                    .script-content h3 {
                      color: #fb923c;
                      font-size: 1.25rem;
                      margin: 1.5rem 0 1rem 0;
                      padding-left: 1rem;
                      border-left: 3px solid #fb923c;
                    }
                    
                    .section-header {
                      display: flex;
                      align-items: center;
                      gap: 1rem;
                      padding: 1rem 1.5rem;
                      background: rgba(15, 23, 42, 0.8);
                      border-radius: 12px;
                      border: 1px solid rgba(148, 163, 184, 0.2);
                      margin: 1.5rem 0;
                      font-weight: 600;
                      font-size: 1.125rem;
                      color: #e2e8f0;
                    }
                    
                    
                    .script-content hr {
                      border: none;
                      height: 2px;
                      background: linear-gradient(90deg, transparent, rgba(148, 163, 184, 0.3), transparent);
                      margin: 2rem 0;
                    }
                    
                    .script-content p {
                      line-height: 1.7;
                      margin-bottom: 1rem;
                      text-align: left;
                    }
                  `}</style>
                  <div className="prose prose-invert prose-lg max-w-none">
                    <div 
                      className="text-gray-300 leading-relaxed script-content"
                      dangerouslySetInnerHTML={{ 
                        __html: preprocessScript(generatedScript.fullScript)
                          // Convert markdown headers to HTML
                          .replace(/^# (.*$)/gim, '<h1>$1</h1>')
                          .replace(/^## (.*$)/gim, '<h2>$1</h2>')
                          .replace(/^### (.*$)/gim, '<h3>$1</h3>')
                          .replace(/^#### (.*$)/gim, '<h4>$1</h4>')
                          
                          // Convert separators to clean dividers
                          .replace(/^---$/gm, '<hr>')
                          
                          // Convert remaining markdown formatting
                          .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                          .replace(/\*(.*?)\*/g, '<em>$1</em>')
                          
                          // Split into sections and handle each part
                          .split('\n\n')
                          .map(section => {
                            const trimmed = section.trim()
                            if (!trimmed) return ''
                            
                            // Skip if already HTML
                            if (trimmed.startsWith('<')) {
                              return trimmed
                            }
                            
                            // Check if it's a header line
                            if (trimmed.match(/^#{1,4}\s/)) {
                              return trimmed
                            }
                            
                            // Check if it's a separator
                            if (trimmed === '---') {
                              return '<hr>'
                            }
                            
                            // Convert to paragraph with proper spacing
                            return `<p>${trimmed}</p>`
                          })
                          .filter(Boolean)
                          .join('\n\n')
                      }}
                    />
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  )
}