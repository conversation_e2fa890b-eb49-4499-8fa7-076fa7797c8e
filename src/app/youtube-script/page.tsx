'use client'

import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import {
  ArrowLeft,
  Video,
  Sparkles,
  Clock,
  Users,
  Target,
  FileText,
  Download,
  Copy,
  Play,
  BarChart3,
  CheckCircle,
  Loader2,
  Youtube,
  Brain,
  Cpu,
  Zap,
  Star,
  TrendingUp,
  Eye,
  MessageCircle,
  Share2,
  Settings,
  Wand2,
  Rocket,
  Globe
} from 'lucide-react'
import Link from 'next/link'
import ReactMarkdown from 'react-markdown'



// Video duration options
const DURATION_OPTIONS = [
  { id: '1-3', label: '1-3 minutes', icon: '⚡', description: 'Quick, punchy content' },
  { id: '3-7', label: '3-7 minutes', icon: '🎯', description: 'Standard engagement' },
  { id: '7-15', label: '7-15 minutes', icon: '📚', description: 'In-depth content' },
  { id: '15+', label: '15+ minutes', icon: '🎬', description: 'Long-form content' }
]

// Video styles
const STYLE_OPTIONS = [
  { id: 'educational', label: 'Educational', icon: '🎓', description: 'Teach and inform' },
  { id: 'entertainment', label: 'Entertainment', icon: '🎪', description: 'Fun and engaging' },
  { id: 'tutorial', label: 'Tutorial', icon: '🛠️', description: 'Step-by-step guide' },
  { id: 'review', label: 'Review', icon: '⭐', description: 'Product/service review' },
  { id: 'vlog', label: 'Vlog', icon: '📱', description: 'Personal storytelling' },
  { id: 'explainer', label: 'Explainer', icon: '💡', description: 'Concept breakdown' }
]

// Target audiences
const AUDIENCE_OPTIONS = [
  { id: 'general', label: 'General Audience', description: 'Broad appeal content' },
  { id: 'tech', label: 'Tech Enthusiasts', description: 'Technology-focused viewers' },
  { id: 'business', label: 'Business Professionals', description: 'Career and business content' },
  { id: 'students', label: 'Students & Learners', description: 'Educational content seekers' },
  { id: 'creators', label: 'Content Creators', description: 'Fellow creators and influencers' },
  { id: 'lifestyle', label: 'Lifestyle & Wellness', description: 'Health, fitness, and lifestyle' }
]

// Analysis phases
const ANALYSIS_PHASES = [
  { id: 'search', label: 'YouTube Search', description: 'Finding top videos for your topic' },
  { id: 'captions', label: 'Caption Extraction', description: 'Extracting transcripts from videos' },
  { id: 'analysis', label: 'Pattern Analysis', description: 'Analyzing hooks and engagement strategies' },
  { id: 'generation', label: 'Gemini Generation', description: 'Creating your optimized script with Gemini' }
]

interface AnalysisProgress {
  currentPhase: number
  phases: {
    search: { status: 'pending' | 'running' | 'complete', data?: any }
    captions: { status: 'pending' | 'running' | 'complete', data?: any }
    analysis: { status: 'pending' | 'running' | 'complete', data?: any }
    generation: { status: 'pending' | 'running' | 'complete', data?: any }
  }
}

interface GeneratedScript {
  title: string
  hook: string
  introduction: string
  mainContent: string
  conclusion: string
  fullScript: string
  timestamps: Array<{ time: string, section: string, content: string }>
  engagementElements: Array<{ type: string, timing: string, description: string }>
  visualCues: Array<{ timing: string, suggestion: string }>
  metadata: {
    estimatedLength: string
    keyTopics: string[]
    engagementScore: number
    researchVideos: Array<{ title: string, views: number, engagement: number }>
  }
}

export default function YouTubeScriptGeneratorPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  // Form state
  const [topic, setTopic] = useState('')
  const [duration, setDuration] = useState('3-7')
  const [style, setStyle] = useState('educational')
  const [targetAudience, setTargetAudience] = useState('general')
  const [additionalNotes, setAdditionalNotes] = useState('')

  // Generation state
  const [isGenerating, setIsGenerating] = useState(false)
  const [analysisProgress, setAnalysisProgress] = useState<AnalysisProgress>({
    currentPhase: -1,
    phases: {
      search: { status: 'pending' },
      captions: { status: 'pending' },
      analysis: { status: 'pending' },
      generation: { status: 'pending' }
    }
  })
  const [generatedScript, setGeneratedScript] = useState<GeneratedScript | null>(null)
  const [activeTab, setActiveTab] = useState('input')
  const [activeScriptTab, setActiveScriptTab] = useState('full')
  const [copied, setCopied] = useState(false)

  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login')
    }
  }, [status, router])

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin w-12 h-12 border-2 border-red-400 border-t-transparent rounded-full mx-auto"></div>
          <p className="text-gray-400">Loading...</p>
        </div>
      </div>
    )
  }

  if (status === 'unauthenticated') {
    return null
  }

  const handleGenerate = async () => {
    if (!topic.trim()) return

    setIsGenerating(true)
    setActiveTab('progress')
    
    // Generate session ID for progress tracking
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    // Reset progress
    setAnalysisProgress({
      currentPhase: 0,
      phases: {
        search: { status: 'pending' },
        captions: { status: 'pending' },
        analysis: { status: 'pending' },
        generation: { status: 'pending' }
      }
    })

    // Set up SSE for real-time progress
    const eventSource = new EventSource(`/api/generate/youtube-script/progress?sessionId=${sessionId}`)
    
    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        
        if (data.type === 'progress') {
          console.log('Progress update:', data)
          
          // Update progress based on phase
          const phaseMap: Record<string, keyof AnalysisProgress['phases']> = {
            'search': 'search',
            'captions': 'captions',
            'analysis': 'analysis',
            'generation': 'generation'
          }
          
          const phaseKey = phaseMap[data.phase]
          if (phaseKey) {
            setAnalysisProgress(prev => ({
              currentPhase: data.currentPhase || prev.currentPhase,
              phases: {
                ...prev.phases,
                [phaseKey]: { 
                  status: data.phase === 'completed' ? 'complete' : 'running',
                  data: data
                }
              }
            }))
          }
        } else if (data.type === 'completed') {
          console.log('Generation completed')
          eventSource.close()
        }
      } catch (error) {
        console.error('SSE parsing error:', error)
      }
    }
    
    eventSource.onerror = (error) => {
      console.error('SSE error:', error)
      eventSource.close()
    }

    try {
      const response = await fetch('/api/generate/youtube-script', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          topic,
          duration,
          style,
          targetAudience,
          additionalNotes
        })
      })

      const data = await response.json()
      
      if (data.success) {
        setGeneratedScript(data.script)
        setActiveTab('script')
        
        // Mark all phases as complete
        setAnalysisProgress(prev => ({
          currentPhase: 3,
          phases: {
            search: { status: 'complete' },
            captions: { status: 'complete' },
            analysis: { status: 'complete' },
            generation: { status: 'complete' }
          }
        }))
      } else {
        alert('Error: ' + data.error)
      }
    } catch (error) {
      console.error('Generation error:', error)
      alert('Failed to generate YouTube script')
    } finally {
      setIsGenerating(false)
      eventSource.close()
    }
  }

  const copyToClipboard = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy:', error)
    }
  }

  const downloadScript = () => {
    if (!generatedScript) return
    
    const element = document.createElement('a')
    const file = new Blob([generatedScript.fullScript], { type: 'text/plain' })
    element.href = URL.createObjectURL(file)
    element.download = `youtube-script-${topic.replace(/[^a-z0-9]/gi, '_')}.txt`
    document.body.appendChild(element)
    element.click()
    document.body.removeChild(element)
  }

  return (
    <>
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -inset-10 opacity-30">
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              rotate: [0, 180, 360],
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              ease: "linear"
            }}
            className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-500/30 to-pink-500/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1.2, 1, 1.2],
              rotate: [360, 180, 0],
            }}
            transition={{
              duration: 25,
              repeat: Infinity,
              ease: "linear"
            }}
            className="absolute top-3/4 right-1/4 w-96 h-96 bg-gradient-to-r from-blue-500/30 to-cyan-500/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1, 1.3, 1],
              x: [0, 100, 0],
              y: [0, -50, 0],
            }}
            transition={{
              duration: 30,
              repeat: Infinity,
              ease: "linear"
            }}
            className="absolute top-1/2 left-1/2 w-96 h-96 bg-gradient-to-r from-indigo-500/30 to-purple-500/30 rounded-full blur-3xl"
          />
        </div>

        {/* Grid Pattern */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]"></div>
      </div>

      {/* Header */}
      <div className="relative z-10">
        <div className="border-b border-white/10 bg-black/20 backdrop-blur-2xl">
          <div className="max-w-7xl mx-auto px-6 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-6">
                <Link href="/dashboard">
                  <motion.button
                    whileHover={{ scale: 1.1, rotate: -5 }}
                    whileTap={{ scale: 0.95 }}
                    className="group p-3 rounded-2xl bg-gradient-to-r from-slate-800/80 to-slate-700/80 backdrop-blur-sm border border-white/20 hover:border-white/40 transition-all duration-300"
                  >
                    <ArrowLeft className="w-5 h-5 text-white group-hover:text-purple-300 transition-colors" />
                  </motion.button>
                </Link>

                <div className="flex items-center space-x-4">
                  <div className="relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-red-500 via-purple-500 to-blue-500 rounded-3xl blur opacity-75 animate-pulse"></div>
                    <div className="relative p-4 bg-gradient-to-r from-red-500 via-purple-500 to-blue-500 rounded-3xl">
                      <Youtube className="w-8 h-8 text-white" />
                    </div>
                  </div>

                  <div>
                    <h1 className="text-3xl font-bold bg-gradient-to-r from-white via-purple-200 to-blue-200 bg-clip-text text-transparent">
                      YouTube Script Generator
                    </h1>
                    <div className="flex items-center space-x-3 mt-1">
                      <div className="flex items-center space-x-2 px-3 py-1 bg-purple-500/20 rounded-full border border-purple-500/30">
                        <Brain className="w-4 h-4 text-purple-400" />
                        <span className="text-sm text-purple-300 font-medium">Kimi-K2</span>
                      </div>
                      <div className="flex items-center space-x-2 px-3 py-1 bg-blue-500/20 rounded-full border border-blue-500/30">
                        <Cpu className="w-4 h-4 text-blue-400" />
                        <span className="text-sm text-blue-300 font-medium">chutes/fp4</span>
                      </div>
                      <div className="flex items-center space-x-2 px-3 py-1 bg-yellow-500/20 rounded-full border border-yellow-500/30">
                        <Zap className="w-4 h-4 text-yellow-400" />
                        <span className="text-sm text-yellow-300 font-medium">AI Powered</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="px-4 py-2 bg-green-500/20 border border-green-500/30 rounded-full backdrop-blur-sm">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-green-300 text-sm font-medium">Online</span>
                  </div>
                </div>

                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="p-3 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 transition-all"
                >
                  <Settings className="w-5 h-5 text-white" />
                </motion.button>
              </div>
            </div>
          </div>
        </div>

        {/* Modern Tab Navigation */}
        <div className="max-w-7xl mx-auto px-6 py-6">
          <div className="flex space-x-2 bg-black/30 backdrop-blur-2xl border border-white/10 rounded-2xl p-2">
            {[
              { id: 'input', label: 'Create Script', icon: Wand2, color: 'from-purple-500 to-pink-500' },
              { id: 'progress', label: 'AI Processing', icon: Brain, color: 'from-blue-500 to-cyan-500', disabled: !isGenerating && analysisProgress.currentPhase === -1 },
              { id: 'script', label: 'Your Script', icon: Rocket, color: 'from-green-500 to-emerald-500', disabled: !generatedScript }
            ].map((tab) => (
              <motion.button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                disabled={tab.disabled}
                whileHover={{ scale: tab.disabled ? 1 : 1.02 }}
                whileTap={{ scale: tab.disabled ? 1 : 0.98 }}
                className={`flex-1 relative overflow-hidden rounded-xl transition-all duration-300 ${
                  activeTab === tab.id
                    ? 'shadow-lg shadow-purple-500/25'
                    : tab.disabled
                      ? 'opacity-50 cursor-not-allowed'
                      : 'hover:shadow-md hover:shadow-white/10'
                }`}
              >
                <div className={`absolute inset-0 bg-gradient-to-r ${tab.color} opacity-${activeTab === tab.id ? '100' : '0'} transition-opacity duration-300`}></div>
                <div className={`relative px-6 py-4 ${activeTab === tab.id ? 'bg-transparent' : 'bg-white/5 hover:bg-white/10'} transition-colors duration-300`}>
                  <div className="flex items-center justify-center space-x-3">
                    <tab.icon className={`w-5 h-5 ${activeTab === tab.id ? 'text-white' : 'text-gray-400'} transition-colors duration-300`} />
                    <span className={`font-medium ${activeTab === tab.id ? 'text-white' : 'text-gray-300'} transition-colors duration-300`}>
                      {tab.label}
                    </span>
                  </div>
                </div>
              </motion.button>
            ))}
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-6 pb-12">
          <AnimatePresence mode="wait">
            {activeTab === 'input' && (
              <motion.div
                key="input"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className="space-y-8"
              >
                {/* Hero Section */}
                <div className="text-center py-8">
                  <motion.div
                    initial={{ scale: 0.9, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 0.1 }}
                  >
                    <h2 className="text-4xl font-bold bg-gradient-to-r from-white via-purple-200 to-blue-200 bg-clip-text text-transparent mb-4">
                      Create Your Perfect YouTube Script
                    </h2>
                    <p className="text-xl text-gray-300 max-w-2xl mx-auto">
                      Powered by advanced AI to generate engaging, optimized scripts that captivate your audience
                    </p>
                  </motion.div>
                </div>

                {/* Main Input Card */}
                <motion.div
                  initial={{ scale: 0.95, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ delay: 0.2 }}
                  className="relative"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 via-pink-500/20 to-blue-500/20 rounded-3xl blur-xl"></div>
                  <div className="relative bg-black/40 backdrop-blur-2xl border border-white/20 rounded-3xl p-8">
                    <div className="flex items-center space-x-3 mb-6">
                      <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl">
                        <Target className="w-6 h-6 text-white" />
                      </div>
                      <h3 className="text-2xl font-bold text-white">Script Details</h3>
                    </div>

                    <div className="space-y-6">
                      {/* Topic Input */}
                      <div>
                        <label className="block text-sm font-semibold text-gray-300 mb-3 flex items-center">
                          <Sparkles className="w-4 h-4 mr-2 text-purple-400" />
                          What's your video about? *
                        </label>
                        <div className="relative">
                          <input
                            type="text"
                            value={topic}
                            onChange={(e) => setTopic(e.target.value)}
                            placeholder="e.g., How to start a successful YouTube channel in 2025"
                            className="w-full px-6 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl text-white placeholder-gray-400 focus:bg-white/15 focus:border-purple-500/50 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-lg"
                          />
                          <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                            <Globe className="w-5 h-5 text-gray-400" />
                          </div>
                        </div>
                      </div>

                      {/* Additional Notes */}
                      <div>
                        <label className="block text-sm font-semibold text-gray-300 mb-3 flex items-center">
                          <FileText className="w-4 h-4 mr-2 text-blue-400" />
                          Additional Notes (optional)
                        </label>
                        <textarea
                          value={additionalNotes}
                          onChange={(e) => setAdditionalNotes(e.target.value)}
                          placeholder="Any specific requirements, tone, or focus areas you'd like to include..."
                          rows={4}
                          className="w-full px-6 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl text-white placeholder-gray-400 focus:bg-white/15 focus:border-blue-500/50 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300 resize-none"
                        />
                      </div>
                    </div>
                  </div>
                </motion.div>

                {/* Configuration Grid */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Duration Card */}
                  <motion.div
                    initial={{ scale: 0.95, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 0.3 }}
                    className="relative group"
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-orange-500/20 to-red-500/20 rounded-2xl blur-lg group-hover:blur-xl transition-all duration-300"></div>
                    <div className="relative bg-black/40 backdrop-blur-2xl border border-white/20 rounded-2xl p-6 hover:border-orange-500/30 transition-all duration-300">
                      <div className="flex items-center space-x-3 mb-6">
                        <div className="p-2 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl">
                          <Clock className="w-5 h-5 text-white" />
                        </div>
                        <h3 className="text-lg font-bold text-white">Duration</h3>
                      </div>
                      <div className="space-y-3">
                        {DURATION_OPTIONS.map((option) => (
                          <motion.button
                            key={option.id}
                            onClick={() => setDuration(option.id)}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            className={`w-full p-4 rounded-xl border text-left transition-all duration-300 ${
                              duration === option.id
                                ? 'bg-gradient-to-r from-orange-500/20 to-red-500/20 border-orange-500/50 text-white shadow-lg shadow-orange-500/10'
                                : 'bg-white/5 border-white/20 text-gray-300 hover:bg-white/10 hover:border-white/30'
                            }`}
                          >
                            <div className="flex items-center space-x-3">
                              <span className="text-2xl">{option.icon}</span>
                              <div>
                                <div className="font-semibold">{option.label}</div>
                                <div className="text-sm opacity-75">{option.description}</div>
                              </div>
                            </div>
                          </motion.button>
                        ))}
                      </div>
                    </div>
                  </motion.div>

                  {/* Style Card */}
                  <motion.div
                    initial={{ scale: 0.95, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 0.4 }}
                    className="relative group"
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-2xl blur-lg group-hover:blur-xl transition-all duration-300"></div>
                    <div className="relative bg-black/40 backdrop-blur-2xl border border-white/20 rounded-2xl p-6 hover:border-purple-500/30 transition-all duration-300">
                      <div className="flex items-center space-x-3 mb-6">
                        <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl">
                          <Video className="w-5 h-5 text-white" />
                        </div>
                        <h3 className="text-lg font-bold text-white">Style</h3>
                      </div>
                      <div className="space-y-3">
                        {STYLE_OPTIONS.map((option) => (
                          <motion.button
                            key={option.id}
                            onClick={() => setStyle(option.id)}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            className={`w-full p-4 rounded-xl border text-left transition-all duration-300 ${
                              style === option.id
                                ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 border-purple-500/50 text-white shadow-lg shadow-purple-500/10'
                                : 'bg-white/5 border-white/20 text-gray-300 hover:bg-white/10 hover:border-white/30'
                            }`}
                          >
                            <div className="flex items-center space-x-3">
                              <span className="text-2xl">{option.icon}</span>
                              <div>
                                <div className="font-semibold">{option.label}</div>
                                <div className="text-sm opacity-75">{option.description}</div>
                              </div>
                            </div>
                          </motion.button>
                        ))}
                      </div>
                    </div>
                  </motion.div>

                  {/* Target Audience Card */}
                  <motion.div
                    initial={{ scale: 0.95, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 0.5 }}
                    className="relative group"
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-2xl blur-lg group-hover:blur-xl transition-all duration-300"></div>
                    <div className="relative bg-black/40 backdrop-blur-2xl border border-white/20 rounded-2xl p-6 hover:border-blue-500/30 transition-all duration-300">
                      <div className="flex items-center space-x-3 mb-6">
                        <div className="p-2 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl">
                          <Users className="w-5 h-5 text-white" />
                        </div>
                        <h3 className="text-lg font-bold text-white">Audience</h3>
                      </div>
                      <div className="space-y-3">
                        {AUDIENCE_OPTIONS.map((option) => (
                          <motion.button
                            key={option.id}
                            onClick={() => setTargetAudience(option.id)}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            className={`w-full p-4 rounded-xl border text-left transition-all duration-300 ${
                              targetAudience === option.id
                                ? 'bg-gradient-to-r from-blue-500/20 to-cyan-500/20 border-blue-500/50 text-white shadow-lg shadow-blue-500/10'
                                : 'bg-white/5 border-white/20 text-gray-300 hover:bg-white/10 hover:border-white/30'
                            }`}
                          >
                            <div className="font-semibold">{option.label}</div>
                            <div className="text-sm opacity-75">{option.description}</div>
                          </motion.button>
                        ))}
                      </div>
                    </div>
                  </motion.div>
                </div>

                {/* Generate Button */}
                <motion.div
                  initial={{ scale: 0.95, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ delay: 0.6 }}
                  className="text-center pt-8"
                >
                  <div className="relative inline-block">
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-500 via-pink-500 to-orange-500 rounded-2xl blur-lg opacity-75 animate-pulse"></div>
                    <motion.button
                      onClick={handleGenerate}
                      disabled={!topic.trim() || isGenerating}
                      whileHover={{ scale: 1.05, y: -2 }}
                      whileTap={{ scale: 0.95 }}
                      className="relative px-12 py-6 bg-gradient-to-r from-purple-600 via-pink-600 to-orange-600 text-white font-bold text-xl rounded-2xl hover:from-purple-700 hover:via-pink-700 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 flex items-center space-x-3 shadow-2xl shadow-purple-500/25"
                    >
                      {isGenerating ? (
                        <>
                          <Loader2 className="w-6 h-6 animate-spin" />
                          <span>Creating Your Script...</span>
                        </>
                      ) : (
                        <>
                          <Rocket className="w-6 h-6" />
                          <span>Generate My Script</span>
                          <Sparkles className="w-6 h-6" />
                        </>
                      )}
                    </motion.button>
                  </div>

                  {/* Feature Highlights */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
                    {[
                      { icon: Brain, label: 'AI-Powered', desc: 'Advanced Kimi-K2 model' },
                      { icon: TrendingUp, label: 'Optimized', desc: 'For maximum engagement' },
                      { icon: Star, label: 'Professional', desc: 'YouTube-ready scripts' }
                    ].map((feature, index) => (
                      <motion.div
                        key={feature.label}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.7 + index * 0.1 }}
                        className="text-center p-4 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"
                      >
                        <feature.icon className="w-8 h-8 mx-auto mb-2 text-purple-400" />
                        <h4 className="font-semibold text-white">{feature.label}</h4>
                        <p className="text-sm text-gray-400">{feature.desc}</p>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              </motion.div>
            )}

            {activeTab === 'progress' && (
              <motion.div
                key="progress"
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{ duration: 0.3 }}
                className="space-y-8"
              >
                {/* Progress Header */}
                <div className="text-center">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                    className="w-20 h-20 mx-auto mb-6 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center"
                  >
                    <Brain className="w-10 h-10 text-white" />
                  </motion.div>
                  <h2 className="text-4xl font-bold bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent mb-4">
                    AI is Creating Your Script
                  </h2>
                  <p className="text-xl text-gray-300 max-w-2xl mx-auto">
                    Our advanced Kimi-K2 model is analyzing trends and crafting the perfect script for your audience
                  </p>
                </div>

                {/* Modern Progress Card */}
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.2 }}
                  className="relative"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 via-pink-500/20 to-blue-500/20 rounded-3xl blur-xl"></div>
                  <div className="relative bg-black/40 backdrop-blur-2xl border border-white/20 rounded-3xl p-8">
                    <div className="space-y-6">
                      {ANALYSIS_PHASES.map((phase, index) => {
                        const phaseKey = phase.id as keyof AnalysisProgress['phases']
                        const phaseStatus = analysisProgress.phases[phaseKey]?.status || 'pending'
                        const isActive = analysisProgress.currentPhase === index
                        const isComplete = phaseStatus === 'complete'
                        const isRunning = phaseStatus === 'running'

                        return (
                          <motion.div
                            key={phase.id}
                            initial={{ x: -20, opacity: 0 }}
                            animate={{ x: 0, opacity: 1 }}
                            transition={{ delay: index * 0.1 }}
                            className="flex items-center space-x-6"
                          >
                            <div className={`relative w-16 h-16 rounded-2xl flex items-center justify-center transition-all duration-500 ${
                              isComplete
                                ? 'bg-gradient-to-r from-green-500 to-emerald-500 shadow-lg shadow-green-500/25'
                                : isRunning
                                ? 'bg-gradient-to-r from-purple-500 to-pink-500 shadow-lg shadow-purple-500/25'
                                : 'bg-white/10 border border-white/20'
                            }`}>
                              {isComplete ? (
                                <CheckCircle className="w-8 h-8 text-white" />
                              ) : isRunning ? (
                                <motion.div
                                  animate={{ rotate: 360 }}
                                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                                >
                                  <Loader2 className="w-8 h-8 text-white" />
                                </motion.div>
                              ) : (
                                <div className="w-6 h-6 rounded-full bg-white/30" />
                              )}

                              {/* Pulse effect for active phase */}
                              {isRunning && (
                                <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-purple-500 to-pink-500 animate-ping opacity-20"></div>
                              )}
                            </div>

                            <div className="flex-1">
                              <h3 className={`text-xl font-bold transition-colors duration-300 ${
                                isComplete ? 'text-green-400' : isRunning ? 'text-white' : 'text-gray-400'
                              }`}>
                                {phase.label}
                              </h3>
                              <p className={`text-sm transition-colors duration-300 ${
                                isRunning ? 'text-gray-300' : 'text-gray-500'
                              }`}>
                                {phase.description}
                              </p>

                              {/* Progress bar for active phase */}
                              {isRunning && (
                                <div className="mt-3 w-full bg-white/10 rounded-full h-2 overflow-hidden">
                                  <motion.div
                                    className="h-full bg-gradient-to-r from-purple-500 to-pink-500"
                                    initial={{ width: "0%" }}
                                    animate={{ width: "100%" }}
                                    transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
                                  />
                                </div>
                              )}
                            </div>
                          </motion.div>
                        )
                      })}
                    </div>
                  </div>
                </motion.div>

                {/* Fun Facts */}
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.4 }}
                  className="text-center"
                >
                  <div className="inline-flex items-center space-x-2 px-6 py-3 bg-white/10 backdrop-blur-sm rounded-full border border-white/20">
                    <Sparkles className="w-5 h-5 text-yellow-400" />
                    <span className="text-gray-300">Did you know? Great scripts can increase engagement by up to 300%!</span>
                  </div>
                </motion.div>
              </motion.div>
            )}

            {activeTab === 'script' && generatedScript && (
              <motion.div
                key="script"
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                transition={{ duration: 0.3 }}
                className="space-y-8"
              >
                {/* Success Header */}
                <div className="text-center">
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ type: "spring", duration: 0.6 }}
                    className="w-20 h-20 mx-auto mb-6 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center"
                  >
                    <CheckCircle className="w-10 h-10 text-white" />
                  </motion.div>
                  <h2 className="text-4xl font-bold bg-gradient-to-r from-white via-green-200 to-emerald-200 bg-clip-text text-transparent mb-4">
                    Your Script is Ready!
                  </h2>
                  <p className="text-xl text-gray-300 max-w-2xl mx-auto">
                    Here's your professionally crafted YouTube script, optimized for maximum engagement
                  </p>
                </div>

                {/* Script Header Card */}
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.2 }}
                  className="relative"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-green-500/20 via-emerald-500/20 to-blue-500/20 rounded-3xl blur-xl"></div>
                  <div className="relative bg-black/40 backdrop-blur-2xl border border-white/20 rounded-3xl p-8">
                    <div className="flex flex-col lg:flex-row lg:items-center justify-between space-y-6 lg:space-y-0">
                      <div className="flex-1">
                        <h3 className="text-2xl font-bold text-white mb-2">{generatedScript.title}</h3>
                        <div className="flex flex-wrap items-center gap-4 text-sm">
                          <div className="flex items-center space-x-2 px-3 py-1 bg-blue-500/20 rounded-full border border-blue-500/30">
                            <Clock className="w-4 h-4 text-blue-400" />
                            <span className="text-blue-300">{generatedScript.metadata.estimatedLength}</span>
                          </div>
                          <div className="flex items-center space-x-2 px-3 py-1 bg-green-500/20 rounded-full border border-green-500/30">
                            <TrendingUp className="w-4 h-4 text-green-400" />
                            <span className="text-green-300">{generatedScript.metadata.engagementScore}% Engagement</span>
                          </div>
                          <div className="flex items-center space-x-2 px-3 py-1 bg-purple-500/20 rounded-full border border-purple-500/30">
                            <Eye className="w-4 h-4 text-purple-400" />
                            <span className="text-purple-300">{generatedScript.metadata.keyTopics.length} Key Topics</span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-3">
                        <motion.button
                          onClick={() => copyToClipboard(generatedScript.fullScript)}
                          whileHover={{ scale: 1.05, y: -2 }}
                          whileTap={{ scale: 0.95 }}
                          className="flex items-center space-x-2 px-6 py-3 bg-white/10 hover:bg-white/20 text-white rounded-xl transition-all duration-300 border border-white/20 hover:border-white/40"
                        >
                          {copied ? <CheckCircle className="w-5 h-5 text-green-400" /> : <Copy className="w-5 h-5" />}
                          <span className="font-medium">{copied ? 'Copied!' : 'Copy Script'}</span>
                        </motion.button>
                        <motion.button
                          onClick={downloadScript}
                          whileHover={{ scale: 1.05, y: -2 }}
                          whileTap={{ scale: 0.95 }}
                          className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-xl transition-all duration-300 shadow-lg shadow-green-500/25"
                        >
                          <Download className="w-5 h-5" />
                          <span className="font-medium">Download</span>
                        </motion.button>
                        <motion.button
                          whileHover={{ scale: 1.05, y: -2 }}
                          whileTap={{ scale: 0.95 }}
                          className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-xl transition-all duration-300 shadow-lg shadow-blue-500/25"
                        >
                          <Share2 className="w-5 h-5" />
                          <span className="font-medium">Share</span>
                        </motion.button>
                      </div>
                    </div>
                  </div>
                </motion.div>

                {/* Script Content Tabs */}
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.4 }}
                  className="relative"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-slate-500/10 via-gray-500/10 to-slate-500/10 rounded-3xl blur-xl"></div>
                  <div className="relative bg-black/40 backdrop-blur-2xl border border-white/20 rounded-3xl overflow-hidden">
                    {/* Script Tabs */}
                    <div className="flex border-b border-white/10">
                      {[
                        { id: 'full', label: 'Complete Script', icon: FileText },
                        { id: 'sections', label: 'By Sections', icon: Target },
                        { id: 'timestamps', label: 'Timeline', icon: Clock }
                      ].map((tab) => (
                        <button
                          key={tab.id}
                          onClick={() => setActiveScriptTab(tab.id)}
                          className={`flex-1 flex items-center justify-center space-x-2 px-6 py-4 transition-all duration-300 ${
                            activeScriptTab === tab.id
                              ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 text-white border-b-2 border-purple-500'
                              : 'text-gray-400 hover:text-white hover:bg-white/5'
                          }`}
                        >
                          <tab.icon className="w-5 h-5" />
                          <span className="font-medium">{tab.label}</span>
                        </button>
                      ))}
                    </div>

                    {/* Tab Content */}
                    <div className="p-8">
                      {activeScriptTab === 'full' && (
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="space-y-6"
                        >
                          <div className="bg-black/30 rounded-2xl p-6 border border-white/10 max-h-96 overflow-y-auto">
                            <ReactMarkdown className="prose prose-invert max-w-none text-gray-300 leading-relaxed">
                              {generatedScript.fullScript}
                            </ReactMarkdown>
                          </div>
                        </motion.div>
                      )}

                      {activeScriptTab === 'sections' && (
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="space-y-6"
                        >
                          {[
                            { title: '🎯 Hook', content: generatedScript.hook, color: 'from-red-500 to-orange-500' },
                            { title: '👋 Introduction', content: generatedScript.introduction, color: 'from-blue-500 to-cyan-500' },
                            { title: '📚 Main Content', content: generatedScript.mainContent, color: 'from-purple-500 to-pink-500' },
                            { title: '🎬 Conclusion', content: generatedScript.conclusion, color: 'from-green-500 to-emerald-500' }
                          ].map((section, index) => (
                            <motion.div
                              key={section.title}
                              initial={{ opacity: 0, x: -20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ delay: index * 0.1 }}
                              className="relative"
                            >
                              <div className={`absolute inset-0 bg-gradient-to-r ${section.color} opacity-10 rounded-2xl blur`}></div>
                              <div className="relative bg-black/30 rounded-2xl p-6 border border-white/10">
                                <h4 className="text-xl font-bold text-white mb-4 flex items-center">
                                  {section.title}
                                </h4>
                                <div className="text-gray-300 leading-relaxed whitespace-pre-wrap">
                                  {section.content}
                                </div>
                              </div>
                            </motion.div>
                          ))}
                        </motion.div>
                      )}

                      {activeScriptTab === 'timestamps' && (
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="space-y-4"
                        >
                          {generatedScript.timestamps.map((timestamp, index) => (
                            <motion.div
                              key={index}
                              initial={{ opacity: 0, x: -20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ delay: index * 0.1 }}
                              className="flex items-start space-x-4 p-4 bg-black/30 rounded-xl border border-white/10 hover:border-white/20 transition-colors"
                            >
                              <div className="px-3 py-1 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg text-white font-mono text-sm font-bold">
                                {timestamp.time}
                              </div>
                              <div className="flex-1">
                                <h5 className="font-semibold text-white mb-1">{timestamp.section}</h5>
                                <p className="text-gray-400 text-sm">{timestamp.content}</p>
                              </div>
                            </motion.div>
                          ))}
                        </motion.div>
                      )}
                    </div>
                  </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
    </>
  )
}